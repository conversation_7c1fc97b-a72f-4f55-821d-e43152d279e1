import React, { useState, useEffect, useRef } from 'react';
import { Send, Loader2, MessageSquare, ThumbsUp, AlertCircle, Bot, User } from 'lucide-react';
import { toast } from 'react-hot-toast';
import fontColorContrast from 'font-color-contrast';
import ChatMessage from './ChatMessage';
import surveyService from '../../services/surveyService';
import { isRTL, getLanguageName } from '../../lib/languages';
import {
  getThankYouTitle,
  getThankYouMessage,
  getWelcomeMessage,
  getAIResponse,
  getUserResponse,
  getPlaceholder,
  getPreviewMode,
  hasTranslations,
} from '../../lib/translations';

const getCookie = (name) => {
  if(typeof document === 'undefined') {
    return null;
  }
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if(parts.length === 2) {
    return parts.pop().split(';').shift();
  }
  return null;
};

const setCookie = (name, value, days = 30) => {
  if(typeof document === 'undefined') {
    return;
  }
  const expires = new Date();
  expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
  document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
};

const deleteCookie = (name) => {
  if(typeof document === 'undefined') {
    return;
  }
  document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;`;
};

const MESSAGE_LIMITS = {
  MIN_LENGTH: 1,
  MAX_LENGTH: 200,
  WARNING_THRESHOLD: 160,
};

function SurveyChat({ surveyId, workspaceId, survey, preview = false }) {
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [responseId, setResponseId] = useState(null);
  const [completed, setCompleted] = useState(false);
  const [thinking, setThinking] = useState(false);
  const [surveyStarted, setSurveyStarted] = useState(false);
  const [showLengthWarning, setShowLengthWarning] = useState(false);
  const [showThank, setShowThank] = useState(false);
  const [showChat, setShowChat] = useState(false);
  const [isReturningUser, setIsReturningUser] = useState(false);
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);

  // Get survey design settings and language
  const design = survey?.design || {};
  const languageCode = survey?.settings?.language || 'en';
  const isRTLMode = isRTL(languageCode);
  const hasLanguageTranslations = hasTranslations(languageCode);

  const chatContainerStyle = {
    backgroundColor: design.backgroundColor || '#f9fafb',
    fontFamily: `${design.font}, Valera Round, Open Sans, sans-serif`,
    direction: isRTLMode ? 'rtl' : 'ltr',
  };

  const chatBackgroundStyle = {
    backgroundColor: design.chatBackgroundColor || '#f9fafb',
  };

  const sendButtonStyle = {
    backgroundColor: design.sendButtonColor || '#3b82f6',
    color: fontColorContrast(design.sendButtonColor || '#3b82f6') || '#ffffff',
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleInputChange = (e) => {
    const { value } = e.target;

    if(value.length > MESSAGE_LIMITS.MAX_LENGTH) {
      return; // Don't allow typing beyond limit
    }

    setInputMessage(value);

    if(value.length >= MESSAGE_LIMITS.WARNING_THRESHOLD) {
      setShowLengthWarning(true);
    } else {
      setShowLengthWarning(false);
    }
  };

  const getCharacterCountStatus = () => {
    const { length } = inputMessage;
    const remaining = MESSAGE_LIMITS.MAX_LENGTH - length;

    if(length >= MESSAGE_LIMITS.MAX_LENGTH) {
      return { status: 'error', remaining: 0, color: '#ef4444' };
    }
    if(length >= MESSAGE_LIMITS.WARNING_THRESHOLD) {
      return { status: 'warning', remaining, color: '#f59e0b' };
    }
    return { status: 'normal', remaining, color: '#6b7280' };
  };

  const characterCount = getCharacterCountStatus();

  useEffect(() => {
    if(!preview) {
      scrollToBottom();
    }
  }, [messages]);

  useEffect(() => {
    if(surveyId) {
      if(preview) {
        loadInitialMessage();
      } else if(messages.length === 0) {
        // Start survey immediately
        startSurvey();
      }
    }
  }, [surveyId, preview]);

  useEffect(() => {
    if(completed || preview) {
      const t = setTimeout(() => setShowThank(true), 30);
      return () => clearTimeout(t);
    }
    setShowThank(false);
  }, [completed, preview]);

  const loadInitialMessage = async () => {
    if(!surveyId) {
      return;
    }

    try {
  
      setLoading(true);
      if(preview) {
        const productName = survey?.title || 'Product';
        const aiMessage = {
          role: 'assistant',
          content: getWelcomeMessage(languageCode, productName),
          timestamp: new Date(),
        };

        const mockUserMessage = {
          role: 'user',
          content: getUserResponse(languageCode, productName),
          timestamp: new Date(),
        };

        const mockAiResponse = {
          role: 'assistant',
          content: getAIResponse(languageCode, productName),
          timestamp: new Date(),
        };

        setMessages([aiMessage, mockUserMessage, mockAiResponse]);

        setResponseId('preview');
        setLoading(false);
        return;
      }

      const initialMessage = await surveyService.getInitialMessage(surveyId);
  

      if(initialMessage) {
        setMessages([{
          role: 'assistant',
          content: initialMessage,
          timestamp: new Date(),
        }]);
        setResponseId('preview'); // Set a dummy responseId to show the chat interface
      }
    } catch(error) {
      toast.error('Failed to load survey preview');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if(!loading && inputRef.current && !completed) {
      inputRef.current.focus();
    }
  }, [loading, completed]);

  // Reload preview when language changes
  useEffect(() => {
    if(preview && survey) {
      loadInitialMessage();
    }
  }, [languageCode, preview, survey]);

  const startSurvey = async () => {
    if(!surveyId) {
      toast.error('Failed to start survey: Missing survey ID');
      return;
    }
    if(surveyStarted) {
      return;
    }
    if(preview) {
      setResponseId('preview');
      setSurveyStarted(true);
      return;
    }
    try {
      setLoading(true);

      const cookieName = `survey_response_${surveyId}`;
      const existingResponseId = getCookie(cookieName);
      if(existingResponseId) {
        try {
          const response = await surveyService.getResponse(existingResponseId);
          if(response && response._id) {
            setIsReturningUser(true);
            setResponseId(response._id);
            setSurveyStarted(true);
            if(response.conversation?.length > 0) {
              setMessages(response.conversation);
            }
            if(response.completedAt || response.isComplete) {
              setCompleted(true);
            }
            setLoading(false);
            return;
          }
        } catch(error) {
          deleteCookie(cookieName);
        }
      }
      const metadata = {
        userAgent: navigator.userAgent,
        language: navigator.language,
        screenSize: `${window.innerWidth}x${window.innerHeight}`,
        timestamp: new Date().toISOString(),
        preview,
      };
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Request timeout')), 10000);
      });
      const responsePromise = surveyService.startSurveyResponse(surveyId, metadata);
      const response = await Promise.race([responsePromise, timeoutPromise]);
      if(response?.error) {
        throw new Error(response.error);
      }
      if(!response || !response._id) {
        throw new Error('Invalid response from server - missing response ID');
      }
      setCookie(cookieName, response._id, 30);
      setResponseId(response._id);
      setSurveyStarted(true);
      if(response.conversation?.length > 0) {
        setMessages(response.conversation);
      }
      if(response.completedAt || response.isComplete) {
        setCompleted(true);
      }
    } catch(error) {
      toast.error(error.message || 'Failed to start survey. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if(!inputMessage.trim() || loading || completed || inputMessage.length > MESSAGE_LIMITS.MAX_LENGTH) {
      return;
    }
    if(inputMessage.trim().length < MESSAGE_LIMITS.MIN_LENGTH) {
      toast.error('Please enter a message');
      return;
    }
    if(!surveyStarted) {
      await startSurvey();
      if(!responseId) {
        toast.error('Failed to start survey');
        return;
      }
    }
    const userMessage = {
      role: 'user',
      content: inputMessage.trim(),
      timestamp: new Date(),
    };
    setMessages((prev) => [...prev, userMessage]);
    setInputMessage('');
    setLoading(true);
    setThinking(true);
    try {
      setTimeout(() => scrollToBottom(), 100);
      await new Promise((resolve) => setTimeout(resolve, 300));
      if(preview) {
        return;
      }
      const response = await surveyService.sendMessage(responseId, userMessage);
      await new Promise((resolve) => setTimeout(resolve, 800));
      setThinking(false);

      if(response.conversation) {
        setMessages(response.conversation);
      }

      if(response.completedAt || response.isComplete) {
        // Add a small delay before showing completion message
        await new Promise((resolve) => setTimeout(resolve, 500));
        setCompleted(true);
      }
    } catch(error) {
      console.error('Failed to send message:', error);
      toast.error('Failed to send message');
      setThinking(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if(preview) {
      setShowChat(true);
      return;
    }
    if(responseId && !showChat) {
      const timer = setTimeout(() => {
        setShowChat(true);
      }, 1000); // Show loading for 1 second

      return () => clearTimeout(timer);
    }
  }, [responseId, showChat, preview]);

  if(!responseId || !showChat) {
    return (
      <div className="flex items-center justify-center h-[600px] rounded-xl shadow-lg">
        <div className="text-center">
          <div className="w-12 h-12 rounded-full backdrop-blur-sm border border-gray-400/20 flex items-center justify-center shadow-lg mx-auto mb-4">
            <Loader2 className="h-6 w-6 text-gray-600 animate-spin" />
          </div>
        </div>
      </div>
    );
  }

  if(!showChat) {
    return null;
  }

  return (
    <div
      className="flex flex-col h-[600px] shadow-xl overflow-hidden bg-gradient-to-br from-gray-50/50 to-white backdrop-blur-sm transition-opacity duration-200 rounded-xl"
      style={chatContainerStyle}
    >
      <div
        className="flex-1 overflow-y-auto px-4 md:px-6 py-6"
        style={{
          ...chatBackgroundStyle,
          scrollbarWidth: 'none', /* Firefox */
          msOverflowStyle: 'none', /* Internet Explorer 10+ */
        }}
      >
        <style jsx>{`
          div::-webkit-scrollbar {
            display: none; /* Chrome, Safari and Opera */
          }
        `}
        </style>
        <div className="max-w-3xl mx-auto space-y-6">
          {messages.map((message, index) => (
            <ChatMessage
              key={index}
              message={message}
              isUser={message.role === 'user'}
              design={design}
              survey={survey}
            />
          ))}

          {thinking && <ChatMessage isTyping message={{}} design={design} survey={survey} />}

          {loading && !thinking && (
            <div className="flex justify-center py-8">
              <div className="w-12 h-12 rounded-full bg-gray-500/10 backdrop-blur-sm border border-gray-400/20 flex items-center justify-center shadow-lg">
                <Loader2 className="h-6 w-6 text-gray-600 animate-spin" />
              </div>
            </div>
          )}
          {(completed || preview) && (
          <div className="flex justify-center my-8 w-full">
            <div className="w-full mx-auto mt-5">
              <div
                className={`rounded-lg shadow-sm border border-gray-100 p-6 text-center transition-all duration-500 ease-out transform ${showThank ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2'}`}
                style={{ backgroundColor: design.thankYouBackgroundColor || '#ffffff' }}
              >
                {/* Icon with  pulse ring */}
                <div className="relative mx-auto mb-4 w-12 h-12">
                  <div
                    className="absolute inset-0 rounded-full animate-ping"
                    style={{ backgroundColor: (design.thankYouIconColor || '#10b981'), opacity: 0.12 }}
                  />
                  <div
                    className="relative w-12 h-12 rounded-full flex items-center justify-center"
                    style={{
                      backgroundColor: design.thankYouIconColor || '#10b981',
                      color: fontColorContrast(design.thankYouIconColor || '#10b981') || '#ffffff',
                    }}
                  >
                    <ThumbsUp className="h-6 w-6" />
                  </div>
                </div>

                {/* Clean text */}
                <h4 className="text-lg font-semibold mb-2" style={{ color: fontColorContrast(design.thankYouBackgroundColor) }}>
                  {getThankYouTitle(languageCode)}
                </h4>
                <p className="text-sm" style={{ color: `${fontColorContrast(design.thankYouBackgroundColor)}90` }}>
                  {getThankYouMessage(languageCode)}
                </p>
              </div>
            </div>
          </div>
          )}
          <div ref={messagesEndRef} />
          {preview && !hasLanguageTranslations && languageCode !== 'en' && (
            <div className="max-w-3xl mx-auto mt-4">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                <div className="flex items-center">
                  <AlertCircle className="w-4 h-4 text-yellow-600 mr-2" />
                  <p className="text-sm text-yellow-800">
                    Preview text is shown in English. Translations for {getLanguageName(languageCode)} are not yet available.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className={`p-4 ${completed ? 'hidden' : ''}`} style={{ 
        backgroundColor: design.chatBackgroundColor || '#f9fafb',
        borderTop: '1px solid rgba(0,0,0,0.05)',
      }}>
        <div className="max-w-3xl mx-auto">
          {preview ? (
            <div className={`flex items-center justify-center ${isRTLMode ? 'space-x-reverse space-x-3' : 'space-x-3'}`}>
              <div className="flex-1 relative">
                <input
                  type="text"
                  disabled
                  placeholder={getPreviewMode(languageCode)}
                  className={`w-full rounded-full px-5 py-3 cursor-not-allowed ${isRTLMode ? 'text-right' : 'text-left'}`}
                  style={{
                    backgroundColor: design.inputBackgroundColor || '#f9fafb',
                    borderColor: design.inputBorderColor || '#e5e7eb',
                    color: fontColorContrast(design.inputBackgroundColor || '#f9fafb') || '#374151',
                    border: `1px solid ${design.inputBorderColor || '#e5e7eb'}`,
                  }}
                />
                <style jsx>{`
                  input::placeholder {
                    color: ${fontColorContrast(design.inputBackgroundColor || '#f9fafb') || '#374151'} !important;
                  }
                `}
                </style>
              </div>
              <div
                className="w-12 h-12 flex items-center justify-center rounded-full"
                style={sendButtonStyle}
              >
                <Send className="h-5 w-5" />
              </div>
            </div>
          ) : (
            <form
              onSubmit={handleSubmit}
              className="flex flex-col space-y-2"
            >
              <div className={`flex items-center ${isRTLMode ? 'space-x-reverse space-x-3' : 'space-x-3'}`}>
                <div className="flex-1 relative">
                  <input
                    ref={inputRef}
                    type="text"
                    value={inputMessage}
                    onChange={handleInputChange}
                    disabled={loading}
                    placeholder={getPlaceholder(languageCode)}
                    maxLength={MESSAGE_LIMITS.MAX_LENGTH}
                    className={`w-full rounded-full px-5 py-3 focus:outline-none shadow-sm transition-all duration-200 ${
                      characterCount.status === 'error' ? 'border-red-300'
                        : characterCount.status === 'warning' ? 'border-yellow-300' : ''
                    } ${isRTLMode ? 'text-right' : 'text-left'}`}
                    style={{
                      backgroundColor: design.inputBackgroundColor || '#f9fafb',
                      borderColor: characterCount.status === 'error' ? '#ef4444'
                        : characterCount.status === 'warning' ? '#f59e0b'
                          : design.inputBorderColor || '#e5e7eb',
                      color: fontColorContrast(design.inputBackgroundColor || '#f9fafb') || '#374151',
                      border: `1px solid ${characterCount.status === 'error' ? '#ef4444'
                        : characterCount.status === 'warning' ? '#f59e0b'
                          : design.inputBorderColor || '#e5e7eb'}`,
                    }}
                  />
                  {inputMessage.length > 0 && (
                    <div className={`absolute ${isRTLMode ? 'left-3' : 'right-3'} top-1/2 transform -translate-y-1/2 pointer-events-none`}>
                      <span className="text-xs text-gray-400 bg-white/90 backdrop-blur-sm px-1.5 py-0.5 rounded-full">
                        {inputMessage.length}/{MESSAGE_LIMITS.MAX_LENGTH}
                      </span>
                    </div>
                  )}

                  <style jsx>{`
                    input::placeholder {
                      color: ${fontColorContrast(design.inputBackgroundColor || '#f9fafb') || '#374151'} !important;
                    }
                  `}
                  </style>
                </div>
                <button
                  type="submit"
                  disabled={!inputMessage.trim() || loading || inputMessage.length > MESSAGE_LIMITS.MAX_LENGTH}
                  style={(!inputMessage.trim() || loading || inputMessage.length > MESSAGE_LIMITS.MAX_LENGTH)
                    ? {}
                    : sendButtonStyle}
                  className={`w-12 h-12 flex items-center justify-center shadow-sm transition-all duration-200 rounded-full ${
                    (!inputMessage.trim() || loading || inputMessage.length > MESSAGE_LIMITS.MAX_LENGTH)
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'text-white hover:shadow-md hover:opacity-90 transform hover:scale-[1.02] active:scale-[0.98]'
                  }`}
                >
                  {loading ? (
                    <Loader2 className="h-5 w-5 animate-spin" />
                  ) : (
                    <Send className="h-5 w-5" />
                  )}
                </button>
              </div>
              
            </form>
          )}
        </div>
      </div>
    </div>
  );
}

export default SurveyChat;
