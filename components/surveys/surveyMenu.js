import {
  MessageSquare,
  Paintbrush,
  BarChart3,
  Users,
  Settings,
} from 'lucide-react';

const surveyMenuItems = [
  {
    sectionTitle: 'SURVEY',
    items: [
      {
        id: 1,
        step: 'content',
        title: 'Content & Details',
        icon: <MessageSquare size={24} />,
      },
      {
        id: 2,
        step: 'design',
        title: 'Design',
        icon: <Paintbrush size={24} />,
      },
      {
        id: 3,
        step: 'settings',
        title: 'Settings',
        icon: <Settings size={24} />,
      },
      {
        id: 4,
        step: 'responses',
        title: 'Responses & Analytics',
        icon: <Users size={24} />,
      },
    ],
  },
];

export default surveyMenuItems;
