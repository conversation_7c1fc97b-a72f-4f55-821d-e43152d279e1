import React, { useEffect, useState, Fragment, useContext, createContext } from 'react';
import Link from 'next/link';
import {
  ChevronLeft,
  MessageSquare,
  ExternalLink,
  Share2,
  CircleCheck,
  Bot,
  Eye,
  Users,
  AlertTriangle,
  ArrowLeft,
  TrendingUp,
  Clock,
  User,
  Globe,
  ChevronRight,
  Lightbulb,
  Sparkles,
  Calendar,
  ChevronDown,
  Star,
  Brain, RefreshCw,
} from 'lucide-react';
import _ from 'lodash';
import moment from 'moment';
import { useRouter } from 'next/router';
import useSWR from 'swr';
import { toast } from 'react-hot-toast';
import { NextSeo } from 'next-seo';
import Toggle from '../widgets/Toggle';
import surveyMenuItems from './surveyMenu';
import SurveyChat from './SurveyChat';
import SurveyAnalytics from './SurveyAnalytics';
import useUser from '../../lib/useUser';
import useWarnIfUnsavedChanges from '../../lib/useWarnIfUnsavedChanges';
import surveyService from '../../services/surveyService';
import Loading from '../common/Loading';
import ButtonLoading from '../common/ButtonLoading';
import FontPicker from '../common/FontPicker';
import DeleteSurveyModal from '../modals/DeleteSurveyModal';
import ToggleSurveyModal from '../modals/ToggleSurveyModal';
import { fileToBase64 } from '../../lib/utils';
import UpgradeModal from '../modals/UpgradeModal';
import ProBadge from '../common/ProBadge';
import useSurveyResponses from '../../lib/useSurveyResponses';
import LanguageSelector from '../common/LanguageSelector';
import InputSlider from '../widgets/InputSlider';

const renderPageButtons = ({ currentPage, totalPages, handlePageChange }) => {
  const pageButtons = [];
  const maxButtons = 5;
  const sideButtons = Math.floor((maxButtons - 1) / 2);
  let startPage = Math.max(currentPage - sideButtons, 1);
  let endPage = Math.min(startPage + maxButtons - 1, totalPages);

  if(totalPages > maxButtons) {
    if(endPage === totalPages) {
      startPage = Math.max(endPage - maxButtons + 1, 1);
    } else if(startPage === 1) {
      endPage = Math.min(startPage + maxButtons - 1, totalPages);
    }
  }

  if(startPage > 1) {
    pageButtons.push(
      <button
        key={1}
        onClick={() => handlePageChange(1)}
        className="focus:outline-offset-0 relative inline-flex cursor-pointer items-center px-4 py-1 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-100 focus:z-20"
      >
        1
      </button>,
    );
    if(startPage > 2) {
      pageButtons.push(
        <span
          key="dots1"
          className="focus:outline-offset-0 relative inline-flex items-center px-4 py-1 text-sm font-bold text-gray-700 ring-1 ring-inset ring-gray-300"
        >
          ...
        </span>,
      );
    }
  }

  for(let i = startPage; i <= endPage; i++) {
    pageButtons.push(
      <button
        key={i}
        onClick={() => handlePageChange(i)}
        className={`relative ${
          i === currentPage
            ? 'inline-flex items-center bg-black px-4 py-1 text-sm font-semibold text-white'
            : 'focus:outline-offset-0   border border-gray-100 inline-flex cursor-pointer items-center px-4 py-1 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-100 focus:z-20'
        }`}
      >
        {i}
      </button>,
    );
  }

  if(endPage < totalPages) {
    if(endPage < totalPages - 1) {
      pageButtons.push(
        <span
          key="dots2"
          className="focus:outline-offset-0 relative inline-flex items-center px-4 py-1 text-sm font-bold bird text-gray-700 ring-1 ring-inset ring-gray-300"
        >
          ...
        </span>,
      );
    }
    pageButtons.push(
      <button
        key={totalPages}
        onClick={() => handlePageChange(totalPages)}
        className="focus:outline-offset-0 relative inline-flex cursor-pointer items-center px-4 py-1 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-100 focus:z-20"
      >
        {totalPages}
      </button>,
    );
  }

  return pageButtons;
};

// Pagination Component
function Pagination({ currentPage, totalPages, total, limit, onPageChange, onLimitChange }) {
  return (
    <div className="flex items-center justify-between border-gray-300 bg-white py-3 px-4 rounded-lg border">
      <div className="flex flex-1 justify-between sm:hidden">
        <button
          disabled={currentPage === 1}
          onClick={() => onPageChange(currentPage - 1)}
          className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Previous
        </button>
        <button
          disabled={currentPage === totalPages}
          onClick={() => onPageChange(currentPage + 1)}
          className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Next
        </button>
      </div>

      <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
        <div>
          <p className="text-sm text-gray-700">
            {total > 0 ? (
              <>
                Showing{' '}
                <span className="font-bold">
                  {((currentPage - 1) * limit) + 1}
                  <span className="px-1 font-normal">to</span>
                  {Math.min(currentPage * limit, total)}
                </span>{' '}
                of <span className="font-bold">{total}</span> responses
              </>
            ) : (
              'No responses found'
            )}
          </p>
        </div>

        <div className="flex items-center">
          <div className="mr-3 rounded-md border border-gray-300 shadow-sm">
            <select
              value={limit}
              onChange={(e) => onLimitChange(Number(e.target.value))}
              className="outline outline-neutral-700 mr-2 h-[2.36rem] w-full cursor-pointer rounded border-r-8 border-transparent px-4 text-sm hover:bg-gray-50"
            >
              <option value={10}>10 per page</option>
              <option value={25}>25 per page</option>
              <option value={50}>50 per page</option>
            </select>
          </div>

          <nav className="inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
            <button
              disabled={currentPage === 1}
              onClick={() => onPageChange(currentPage - 1)}
              className="focus:outline-offset-0 relative inline-flex items-center rounded-l-md border border-gray-300 px-3 py-1.5 text-gray-400 hover:bg-gray-100 disabled:bg-gray-50 disabled:opacity-50"
            >
              <ChevronLeft size={20} />
            </button>

            {renderPageButtons({ currentPage, totalPages, handlePageChange: onPageChange })}

            <button
              disabled={currentPage === totalPages}
              onClick={() => onPageChange(currentPage + 1)}
              className="focus:outline-offset-0 relative inline-flex items-center rounded-r-md border border-gray-300 px-3 py-1.5 text-gray-400 hover:bg-gray-100 disabled:bg-gray-50 disabled:opacity-50"
            >
              <ChevronRight size={20} />
            </button>
          </nav>
        </div>
      </div>
    </div>
  );
}

const EditorContext = createContext();

export { EditorContext };

function SurveyEditor(props) {
  const router = useRouter();
  const { surveyId, tab } = router.query;
  const { user, workspace, mutateUser } = useUser();
  const [currentPage, setCurrentPage] = useState(1);
  const { data, error, mutate } = useSWR(
    workspace?.id && router.query.surveyId
      ? `/workspaces/${workspace.id}/surveys/${router.query.surveyId}`
      : null,
    () => surveyService.getSurvey(workspace.id, router.query.surveyId),
    { revalidateOnFocus: false },
  );

  const [survey, setSurvey] = useState({
    title: 'Customer Feedback Survey',
    description: '',
    goal: '',
    design: {
      showLogo: true,
      logo: '',
      backgroundColor: '#fafbfc',
      textColor: '#374151',
      titleColor: '#1f2937',
      font: 'Nunito',
      borderRadius: 'rounded-xl',
      aiResponseBackgroundColor: '#ffffff',
      userResponseBackgroundColor: '#6366f1',
      aiIconColor: '#6366f1',
      userIconColor: '#6b7280',
      sendButtonColor: '#6366f1',
      thankYouIconColor: '#10b981',
      thankYouBackgroundColor: '#ffffff',
      inputBackgroundColor: '#ffffff',
      inputBorderColor: '#d1d5db',
      chatBackgroundColor: '#f8fafc',
      avatar: {
        image: '',
        name: '',
      },
    },
    settings: {
      hideBranding: false,
      autoComplete: true,
      sentimentAnalysis: true,
      language: 'en',
      maxQuestions: 4,
    },
  });

  const [isSavingSurvey, setIsSavingSurvey] = useState(false);
  const [initialSurvey, setInitialSurvey] = useState(survey);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  useWarnIfUnsavedChanges(!_.isEqual(initialSurvey, survey));

  useEffect(() => {
    if(error) {
      toast.error(error.message || 'Failed to load survey');
      router.push(`/${workspace?.id}/surveys`);
      return;
    }
    if(data) {
      setSurvey({ ...survey, ...data });
      setInitialSurvey(data);
    }
  }, [data, error, workspace?.id, router]);

  // Handle URL tab parameter
  useEffect(() => {
    if(tab === 'analytics') {
      setCurrentPage(4);
    } else if(tab === 'responses') {
      setCurrentPage(5);
    }
  }, [tab]);

  if(!workspace?.id || !router.query.surveyId) {
    return <Loading />;
  }

  if(!data && !error) {
    return <Loading />;
  }

  const handleSurveyUpdate = async () => {
    setIsSavingSurvey(true);
    try {
      if(survey.design?.logo?.includes('blob')) {
        const blob = await fetch(survey.design.logo).then((r) => r.blob());
        survey.design.logo = await fileToBase64(blob);
      }
      if(survey.design?.avatar?.image?.includes('blob')) {
        const blob = await fetch(survey.design.avatar.image).then((r) => r.blob());
        survey.design.avatar.image = await fileToBase64(blob);
      }
      const { data: updateData, error } = await surveyService.updateSurvey(
        survey.workspaceId,
        survey._id,
        survey,
      );
      if(error) {
        toast.error(error);
      } else if(updateData) {
        toast.success('Your survey has been updated');
        setInitialSurvey(survey);
        await mutate();
      }
    } catch(err) {
      toast.error('Failed to save survey changes');
    }
    setIsSavingSurvey(false);
  };

  const handleDeleteSurvey = async () => {
    try {
      await surveyService.deleteSurvey(survey.workspaceId, surveyId);
      toast.success('Survey deleted successfully');
      router.push(`/${workspace.id}/surveys`);
    } catch(error) {
      toast.error('Failed to delete survey');
    }
  };

  const handlePublicPrivateToggle = () => {
    setSurvey({
      ...survey,
      isPublic: !survey?.isPublic,
    });
  };

  return (
    <EditorContext.Provider
      value={{
        currentPage,
        setCurrentPage,
        survey,
        setSurvey,
        handleSurveyUpdate,
        isSavingSurvey,
        showDeleteModal,
        setShowDeleteModal,
        handleDeleteSurvey,
        handlePublicPrivateToggle,
        isPublic: survey?.isPublic,
      }}
    >
      <NextSeo title={'Edit Survey'} />

      <div className="flex h-screen bg-gray-50 text-gray-900 antialiased">
        {/* nav */}
        <aside className="focus:outline-none inset-y-0 z-10 block min-h-screen w-20 flex-shrink-0 border-r bg-white lg:w-72">
          <div className="flex h-full flex-col">
            <nav className="tracking-tight lg:tracking-normal">
              <div className="space-y-8 py-3">
                <div className="flex flex-col items-center space-y-4 border-b px-2 pb-3 lg:flex-row lg:space-x-3 lg:space-y-0">
                  <div className=" flex flex-shrink-0">
                    <Link href={`/${workspace?.id}/surveys`}>
                      <a className="hover:opacity-75 flex-shrink-0">
                        <img alt="logo" className="h-12 w-12" src="https://cdn.shapo.io/assets/favicon.png" />
                      </a>
                    </Link>
                  </div>
                  <Link href={`/${workspace?.id}/surveys`}>
                    <a className="max-w-[10rem] flex-grow focus:outline-none inline-flex w-full items-center justify-center rounded-lg border border-gray-300 bg-white px-2 py-1 text-sm text-gray-800 hover:border-gray-800 hover:text-black hover:shadow-md lg:py-2">
                      <ChevronLeft size={15} className="text-gray-600 lg:mr-2" />
                      <span className="hidden lg:block">Your surveys</span>
                      <span className="block lg:hidden">Back</span>
                    </a>
                  </Link>
                </div>

                {surveyMenuItems.map((section) => (
                  <div className="flex-1 space-y-2 px-2 lg:px-3" key={section.sectionTitle}>
                    <p className="mb-5 text-xs font-extrabold text-gray-700 lg:pl-1 lg:text-sm">
                      {section.sectionTitle}
                    </p>
                    {section.items.map((item) => (
                      <MenuItem key={item.title} item={item} />
                    ))}
                  </div>
                ))}
              </div>
            </nav>
          </div>
        </aside>

        {/* page details */}
        <section className={`dark:bg-darker focus:outline-none static inset-y-0 z-10 h-full ${currentPage > 3 ? 'flex-grow' : 'w-96 flex-shrink-0'} border-r bg-white dark:border-indigo-800`}>
          <div className="flex h-full flex-col">
            {/* Top bar for analytics and responses pages */}
            {currentPage > 3 && <AnalyticsTopBar />}

            {currentPage === 1 && <ContentPage />}
            {currentPage === 2 && <DesignPage />}
            {currentPage === 3 && <SettingsPage />}
            {currentPage === 4 && <AnalyticsPage />}
            {currentPage === 5 && <ResponsesPage />}
          </div>
        </section>

        {/* preview - only show for content, AI, design, and settings pages */}
        {currentPage <= 3 ? (
          <PreviewContainer>
            <div
              className="min-h-full"
              style={{
                backgroundColor: survey.design.backgroundColor,
                fontFamily: survey.design.font,
              }}
            >
              {survey.design?.showLogo !== false && (
                <div className="flex justify-center pt-8 pb-4">
                  <img
                    className="h-16"
                    src={survey.design?.logo || 'https://cdn.shapo.io/assets/form-placeholder-logo.svg'}
                    alt="Shapo"
                  />
                </div>
              )}

              <div className="py-8 px-4 sm:px-6 lg:px-8">
                <div className="max-w-3xl mx-auto">
                  <div className="text-center mb-8">
                    <h1
                      className="text-3xl font-bold mb-4"
                      style={{ color: survey.design.titleColor }}
                    >
                      {survey.title}
                    </h1>
                    {survey.description && (
                      <p
                        className="text-lg mb-4"
                        style={{ color: survey.design.textColor }}
                      >
                        {survey.description}
                      </p>
                    )}
                  </div>

                  <SurveyChat
                    surveyId={survey._id}
                    workspaceId={workspace?.id}
                    survey={survey}
                    preview
                  />
                </div>
              </div>

              <div className="text-center pb-6 border-gray-200">
                {survey.settings?.hideBranding !== true && (
                <ShapoBranding />
                )}
              </div>
            </div>
          </PreviewContainer>
        ) : null}

      </div>
    </EditorContext.Provider>
  );
}

function ShapoBranding() {
  return (
    <div className=" justify-center items-center bg-white inline-block rounded-full">
      <a
        style={{ fontFamily: 'Nunito' }}
        href={'https://shapo.io?ref=form-branding'}
        target="_blank"
        className="flex items-center justify-center rounded-full px-2.5 py-1 group"
        rel="noopener"
      >
        <span className="text-gray-600 text-sm font-medium group-hover:opacity-75">
          Powered by
        </span>
        <img
          className="ml-1 h-5 w-16 group-hover:opacity-75"
          src="https://cdn.shapo.io/assets/logo-sm.png"
        />
      </a>
    </div>
  );
}

function MenuItem({ item }) {
  const { currentPage, setCurrentPage } = useContext(EditorContext);

  const handleClick = (e) => {
    e.preventDefault();
    e.stopPropagation();

    setCurrentPage(item.id);
    // Log after a short delay to see if the state actually changed
    setTimeout(() => {

    }, 100);
  };

  return (
    <button
      type="button"
      onClick={handleClick}
      className={` ${item.id === currentPage ? 'border-rose-500 bg-rose-50/80 font-bold text-rose-500 lg:border-l-8' : 'select-none border-transparent hover:bg-gray-50 hover:text-black'} flex select-none items-center justify-center rounded-md p-2.5 px-3 font-medium text-gray-600 hover:cursor-pointer lg:justify-start lg:border-l-8 w-full`}
    >
      <span className="w-7">{item.icon}</span>
      <span className="hidden lg:ml-2.5 lg:block">{item.title}</span>
    </button>
  );
}

function PageHeader({ title, description, children }) {
  return (
    <div className="mb-6 flex-shrink-0 rounded-lg bg-gray-50 p-3">
      <div className="">
        <h2 className="pb-1 text-xl font-bold">{title}</h2>
        {description && <p className="text-base font-medium text-gray-700">{description}</p>}
        {children && children}
      </div>
    </div>
  );
}

function PageWrapper({ children }) {
  return <div className="h-full max-h-screen overflow-y-auto p-4">{children}</div>;
}

function PreviewContainer({ children }) {
  const {
    handleSurveyUpdate,
    isSavingSurvey,
    survey,
    currentPage,
    showDeleteModal,
    setShowDeleteModal,
    handleDeleteSurvey,
  } = useContext(EditorContext);

  const copylink = (e) => {
    navigator.clipboard.writeText(`${process.env.NEXT_PUBLIC_FRONT}/surveys/${survey._id}`);
    toast.success('Copied survey link to clipboard');
  };

  return (
    <section className="block h-screen min-h-screen min-w-[60vh] flex-grow overflow-hidden">
      <div className="mb-2 flex w-full items-center justify-between border-b bg-white p-3">
        <div className="hidden w-full justify-start 2xl:flex">
          <div className="flex flex-col items-start leading-tight">
            <span className="border-b border-dashed border-gray-900 font-bold">{survey?.name}</span>
          </div>
        </div>
        <div className="w-full">
          <div className="flex items-center justify-end space-x-3">
            <ToggleSurveyModal survey={survey} />
            <button
              onClick={copylink}
              className="flex h-10 items-center justify-center space-x-2 rounded-lg border border-gray-300 bg-white px-3 py-1.5 text-center font-bold tracking-tight text-gray-700 drop-shadow-sm hover:bg-gray-50"
            >
              <Share2 size={20} />
              <span className="hidden xl:block">Share</span>
            </button>
            <a
              href={`/surveys/${survey._id}`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex h-10 items-center justify-center space-x-2 rounded-lg border border-gray-300 bg-white px-3 py-1.5 text-center font-bold tracking-tight text-gray-700 shadow-sm hover:bg-gray-50"
            >
              <ExternalLink size={20} />
              <span className="hidden xl:block">View</span>
            </a>

            <ButtonLoading
              type={'submit'}
              disabled={isSavingSurvey}
              isLoading={isSavingSurvey}
              onClick={handleSurveyUpdate}
              size={25}
              className={
                  'flex h-10 items-center justify-center space-x-2 rounded-lg border border-gray-700 bg-gray-900 px-4 py-1.5 text-center font-bold tracking-tight text-white drop-shadow-sm hover:opacity-75 xl:w-24'
                }
            >
              <CircleCheck size={20} />
              <span className="hidden xl:block">Save</span>
            </ButtonLoading>
          </div>
        </div>
      </div>

      <div className="flex flex-1 flex-col items-center p-8 pb-14 pt-8 h-full">
        <div className="relative flex flex-col overflow-hidden bg-gray-50 shadow-xl ring-2 ring-gray-600 duration-300 rounded-md h-full w-full max-h-[calc(100vh-140px)]">
          <div className="flex-1 overflow-y-auto">
            {children}
          </div>
        </div>
      </div>

      <DeleteSurveyModal
        survey={survey}
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onDelete={handleDeleteSurvey}
      />
    </section>
  );
}

function AnalyticsTopBar() {
  const {
    handleSurveyUpdate,
    isSavingSurvey,
    survey,
    showDeleteModal,
    setShowDeleteModal,
    handleDeleteSurvey,
  } = useContext(EditorContext);

  const copylink = (e) => {
    navigator.clipboard.writeText(`${process.env.NEXT_PUBLIC_FRONT}/surveys/${survey._id}`);
    toast.success('Copied survey link to clipboard');
  };

  return (
    <div className="flex w-full items-center justify-between border-b bg-white p-3">
      <div className="hidden w-full justify-start 2xl:flex">
        <div className="flex flex-col items-start leading-tight">
          <span className="border-b border-dashed border-gray-900 font-bold">{survey?.title}</span>
        </div>
      </div>
      <div className="w-full">
        <div className="flex items-center justify-end space-x-3">
          <button
            onClick={() => setShowDeleteModal(true)}
            className="flex h-10 items-center justify-center space-x-2 rounded-lg border border-red-300 bg-white px-3 py-1.5 text-center font-bold tracking-tight text-red-700 drop-shadow-sm hover:bg-red-50"
          >
            <span className="hidden xl:block">Delete</span>
          </button>
          <ToggleSurveyModal survey={survey} />
          <button
            onClick={copylink}
            className="flex h-10 items-center justify-center space-x-2 rounded-lg border border-gray-300 bg-white px-3 py-1.5 text-center font-bold tracking-tight text-gray-700 drop-shadow-sm hover:bg-gray-50"
          >
            <Share2 size={20} />
            <span className="hidden xl:block">Share</span>
          </button>
          <a
            href={`/surveys/${survey._id}`}
            target="_blank"
            rel="noopener noreferrer"
            className="flex h-10 items-center justify-center space-x-2 rounded-lg border border-gray-300 bg-white px-3 py-1.5 text-center font-bold tracking-tight text-gray-700 shadow-sm hover:bg-gray-50"
          >
            <ExternalLink size={20} />
            <span className="hidden xl:block">View</span>
          </a>
          <ButtonLoading
            type={'submit'}
            disabled={isSavingSurvey}
            isLoading={isSavingSurvey}
            onClick={handleSurveyUpdate}
            size={25}
            className={
                'flex h-10 items-center justify-center space-x-2 rounded-lg border border-gray-700 bg-gray-900 px-4 py-1.5 text-center font-bold tracking-tight text-white drop-shadow-sm hover:opacity-75 xl:w-24'
              }
          >
            <CircleCheck size={20} />
            <span className="hidden xl:block">Save</span>
          </ButtonLoading>
        </div>
      </div>

      <DeleteSurveyModal
        survey={survey}
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onDelete={handleDeleteSurvey}
      />
    </div>
  );
}

// ------PAGES-----//

function ContentPage() {
  const { survey, setSurvey } = useContext(EditorContext);

  return (
    <PageWrapper>
      <PageHeader
        title={'Content & Questions'}
        description={'Configure your survey content to give the AI more context.'}
      />
      <div className="">
        <div className="flex flex-col space-y-6">
          <div className="w-full">
            <label htmlFor="title" className="block text-sm font-medium text-black">
              <div className="flex items-center gap-2">Survey Title</div>
            </label>
            <div className="mt-2 flex w-full rounded-md shadow-sm">
              <input
                name="title"
                type="text"
                placeholder="e.g. Customer Feedback Survey"
                value={survey?.title || ''}
                onChange={(e) => setSurvey({
                  ...survey,
                  title: e.target.value,
                })}
                className="block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
              />
            </div>
          </div>

          <div className="w-full">
            <label htmlFor="description" className="block text-sm font-medium text-black">
              <div className="flex items-center gap-2">Survey Description</div>
            </label>
            <div className="mt-2 flex w-full rounded-md shadow-sm">
              <textarea
                name="description"
                rows={3}
                placeholder="Brief description of what this survey is about..."
                value={survey?.description || ''}
                onChange={(e) => setSurvey({
                  ...survey,
                  description: e.target.value,
                })}
                className="block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
              />
            </div>
          </div>

          <div className="w-full">
            <div className="mt-2 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-start space-x-2">
                <div className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <svg className="w-3 h-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="text-sm text-blue-800">
                  <p className="font-medium mb-1">Goal is locked after survey creation</p>
                  <p className="text-blue-700">
                    The survey goal cannot be changed once created to maintain consistency with collected responses.
                    If you need to modify the goal, please create a new survey.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageWrapper>
  );
}

function DesignPage() {
  const { survey, setSurvey } = useContext(EditorContext);

  return (
    <PageWrapper>
      <PageHeader title={'Design'} description={'Customize the look and feel of your survey to match your brand.'} />
      <div className="">
        <div className="flex flex-col space-y-6">
          {/* logo toggle */}
          <div className={'flex w-full items-center justify-between rounded-lg border p-2.5 pl-3 shadow-sm'}>
            <label htmlFor="logoToggle" className="block text-sm font-medium text-black">
              <div className="flex items-center">Show Survey Logo</div>
            </label>
            <Toggle
              value={survey?.design?.showLogo}
              onChange={(checked) => setSurvey({
                ...survey,
                design: { ...survey.design, showLogo: checked },
              })}
            />
          </div>
          {survey?.design?.showLogo && (
            <div className="">
              <label htmlFor="title" className="block text-sm font-medium text-black">
                <div className="mb-2 flex items-center gap-2">Survey logo</div>
              </label>

              <div className="group relative">
                <label className="absolute inset-0 w-full cursor-pointer rounded-md bg-gray-200 opacity-0 group-hover:opacity-20">
                  <input
                    accept="image/*"
                    onChange={(e) => {
                      if(e.target.files.length !== 0) {
                        setSurvey({
                          ...survey,
                          design: {
                            ...survey.design,
                            logo: URL.createObjectURL(e.target.files[0]),
                          },
                        });
                      }
                    }}
                    type="file"
                    className="hidden"
                  />
                </label>

                <div className="flex h-32 w-full flex-col items-center justify-center rounded-md border border-gray-300 bg-white p-2 p-5 text-sm text-gray-600 shadow-sm">
                  <div
                    style={{
                      backgroundImage: `url(${survey?.design?.logo || 'https://cdn.shapo.io/assets/form-placeholder-logo.svg'})`,
                    }}
                    className="h-32 w-32 rounded-2xl bg-contain bg-center bg-no-repeat"
                  />
                  <span className="mb-1 mt-3 px-1 text-xs font-medium text-gray-600">Click to select a new logo</span>
                </div>
              </div>
            </div>
          )}

          <div className="w-full">
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-3">Avatar Image</label>
              <div className="flex items-center">
                <div className="relative group">
                  <div className="h-12 w-12 rounded-full overflow-hidden border-2 border-gray-200 hover:border-blue-400 hover:shadow-lg transition-all duration-200 bg-gradient-to-br from-gray-50 to-gray-100">
                    {survey?.design?.avatar?.image ? (
                      <img
                        src={survey.design.avatar.image}
                        alt="AI Avatar"
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <div className="h-full w-full flex items-center justify-center">
                        <Bot className="h-6 w-6 text-gray-400" />
                      </div>
                    )}
                    {/* Modern overlay */}
                    <div className="absolute rounded-full hover:cursor-pointer inset-0 bg-gradient-to-br from-rose-500/0 to-rose-600/0 group-hover:from-blue-500/20 group-hover:to-rose-600/30 transition-all duration-300 flex items-center justify-center">
                      <div className="opacity-0 group-hover:opacity-100 transition-all duration-300 transform scale-75 group-hover:scale-100">
                        <div className="bg-white/90 backdrop-blur-sm rounded-full p-1 shadow-lg">
                          <svg className="w-3 h-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => {
                      if(e.target.files && e.target.files[0]) {
                        setSurvey({
                          ...survey,
                          design: {
                            ...survey.design,
                            avatar: {
                              ...survey.design?.avatar,
                              image: URL.createObjectURL(e.target.files[0]),
                            },
                          },
                        });
                      }
                    }}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                  />
                </div>
                <div className="flex-1 ml-2">
                  <input
                    type="text"
                    placeholder="AI Assistant Name (optional)"
                    value={survey?.design?.avatar?.name || ''}
                    onChange={(e) => setSurvey({
                      ...survey,
                      design: {
                        ...survey.design,
                        avatar: {
                          ...survey.design?.avatar,
                          name: e.target.value,
                        },
                      },
                    })}
                    className="block w-full rounded-lg border border-gray-200 p-3 text-sm focus:border-blue-400 focus:ring-2 focus:ring-blue-100 transition-all duration-200 bg-white"
                  />
                </div>
                {(survey?.design?.avatar?.image || survey?.design?.avatar?.name) && (
                  <button
                    onClick={() => setSurvey({
                      ...survey,
                      design: {
                        ...survey.design,
                        avatar: { image: '', name: '' },
                      },
                    })}
                    className="ml-2 px-4 py-2.5 text-sm text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200 border border-gray-200 hover:border-red-200"
                  >
                    Reset
                  </button>
                )}
              </div>
            </div>
          </div>

          <div className="w-full">
            <label htmlFor="title" className="block text-sm font-medium text-black">
              <div className="flex  items-center gap-2">Custom Font (GDPR Compliant)</div>
            </label>
            <div className="flex w-full rounded-md mt-2 ">
              <FontPicker
                onChange={(fontFamily) => setSurvey({ ...survey, design: { ...survey.design, font: fontFamily } })}
                value={survey?.design?.font}
                hebrew={survey?.settings?.language === 'he'}
              />
            </div>
          </div>

          {/* colors */}
          <div className="w-full">
            <label htmlFor="title" className="block text-sm font-medium text-black">
              <div className="flex items-center gap-2">Background Color</div>
            </label>
            <div className="mt-2 flex w-full rounded-md shadow-sm">
              <ColorPickerInput
                onChange={(e) => setSurvey({
                  ...survey,
                  design: { ...survey.design, backgroundColor: e.target.value },
                })}
                className="block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                color={survey?.design?.backgroundColor}
                placeholder="#ffffff"
              />
            </div>
          </div>

          <div className="w-full">
            <label htmlFor="title" className="block text-sm font-medium text-black">
              <div className="flex items-center gap-2">Title Color</div>
            </label>
            <div className="mt-2 flex w-full rounded-md shadow-sm">
              <ColorPickerInput
                onChange={(e) => setSurvey({
                  ...survey,
                  design: { ...survey.design, titleColor: e.target.value },
                })}
                className="block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                color={survey?.design?.titleColor}
                placeholder="#101827"
              />
            </div>
          </div>

          <div className="w-full">
            <label htmlFor="title" className="block text-sm font-medium text-black">
              <div className="flex items-center gap-2">Text Color</div>
            </label>
            <div className="mt-2 flex w-full rounded-md shadow-sm">
              <ColorPickerInput
                onChange={(e) => setSurvey({
                  ...survey,
                  design: { ...survey.design, textColor: e.target.value },
                })}
                className="block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                color={survey?.design?.textColor}
                placeholder="#383838"
              />
            </div>
          </div>
          {/* Chat Background */}
          <div className="w-full">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Chat Background</h3>
            <div className="space-y-4">
              <div className="w-full">
                <label htmlFor="chatBackgroundColor" className="block text-sm font-medium text-black">
                  <div className="flex items-center gap-2">Chat Background</div>
                </label>
                <div className="mt-2 flex w-full rounded-md shadow-sm">
                  <ColorPickerInput
                    onChange={(e) => setSurvey({
                      ...survey,
                      design: { ...survey.design, chatBackgroundColor: e.target.value },
                    })}
                    className="block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                    color={survey?.design?.chatBackgroundColor}
                    placeholder="#f9fafb"
                  />
                </div>
              </div>
            </div>
          </div>
          {/* Chat Bubble Colors */}
          <div className="w-full">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Chat Bubble Colors</h3>
            <div className="space-y-4">
              <div className="w-full">
                <label htmlFor="aiResponseBackgroundColor" className="block text-sm font-medium text-black">
                  <div className="flex items-center gap-2">AI Response Background</div>
                </label>
                <div className="mt-2 flex w-full rounded-md shadow-sm">
                  <ColorPickerInput
                    onChange={(e) => setSurvey({
                      ...survey,
                      design: { ...survey.design, aiResponseBackgroundColor: e.target.value },
                    })}
                    className="block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                    color={survey?.design?.aiResponseBackgroundColor}
                    placeholder="#ffffff"
                  />
                </div>
              </div>

              <div className="w-full">
                <label htmlFor="userResponseBackgroundColor" className="block text-sm font-medium text-black">
                  <div className="flex items-center gap-2">User Response Background</div>
                </label>
                <div className="mt-2 flex w-full rounded-md shadow-sm">
                  <ColorPickerInput
                    onChange={(e) => setSurvey({
                      ...survey,
                      design: { ...survey.design, userResponseBackgroundColor: e.target.value },
                    })}
                    className="block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                    color={survey?.design?.userResponseBackgroundColor}
                    placeholder="#3b82f6"
                  />
                </div>
              </div>
              <div className="w-full">
                <label htmlFor="thankYouBackgroundColor" className="block text-sm font-medium text-black">
                  <div className="flex items-center gap-2">Thank You Background</div>
                </label>
                <div className="mt-2 flex w-full rounded-md shadow-sm">
                  <ColorPickerInput
                    onChange={(e) => setSurvey({
                      ...survey,
                      design: { ...survey.design, thankYouBackgroundColor: e.target.value },
                    })}
                    className="block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                    color={survey?.design?.thankYouBackgroundColor}
                    placeholder="#ffffff"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Icon Colors */}
          <div className="w-full">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Icon Colors</h3>
            <div className="space-y-4">
              <div className="w-full">
                <label htmlFor="aiIconColor" className="block text-sm font-medium text-black">
                  <div className="flex items-center gap-2">AI Icon Color</div>
                </label>
                <div className="mt-2 flex w-full rounded-md shadow-sm">
                  <ColorPickerInput
                    onChange={(e) => setSurvey({
                      ...survey,
                      design: { ...survey.design, aiIconColor: e.target.value },
                    })}
                    className="block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                    color={survey?.design?.aiIconColor}
                    placeholder="#3b82f6"
                  />
                </div>
              </div>

              <div className="w-full">
                <label htmlFor="userIconColor" className="block text-sm font-medium text-black">
                  <div className="flex items-center gap-2">User Icon Color</div>
                </label>
                <div className="mt-2 flex w-full rounded-md shadow-sm">
                  <ColorPickerInput
                    onChange={(e) => setSurvey({
                      ...survey,
                      design: { ...survey.design, userIconColor: e.target.value },
                    })}
                    className="block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                    color={survey?.design?.userIconColor}
                    placeholder="#374151"
                  />
                </div>
              </div>

              <div className="w-full">
                <label htmlFor="sendButtonColor" className="block text-sm font-medium text-black">
                  <div className="flex items-center gap-2">Send Button Color</div>
                </label>
                <div className="mt-2 flex w-full rounded-md shadow-sm">
                  <ColorPickerInput
                    onChange={(e) => setSurvey({
                      ...survey,
                      design: { ...survey.design, sendButtonColor: e.target.value },
                    })}
                    className="block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                    color={survey?.design?.sendButtonColor}
                    placeholder="#3b82f6"
                  />
                </div>
              </div>

              <div className="w-full">
                <label htmlFor="thankYouIconColor" className="block text-sm font-medium text-black">
                  <div className="flex items-center gap-2">Thank You Icon Color</div>
                </label>
                <div className="mt-2 flex w-full rounded-md shadow-sm">
                  <ColorPickerInput
                    onChange={(e) => setSurvey({
                      ...survey,
                      design: { ...survey.design, thankYouIconColor: e.target.value },
                    })}
                    className="block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                    color={survey?.design?.thankYouIconColor}
                    placeholder="#10b981"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Input Field Styling */}
          <div className="w-full">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Input Field Styling</h3>
            <div className="space-y-4">
              <div className="w-full">
                <label htmlFor="inputBackgroundColor" className="block text-sm font-medium text-black">
                  <div className="flex items-center gap-2">Input Background</div>
                </label>
                <div className="mt-2 flex w-full rounded-md shadow-sm">
                  <ColorPickerInput
                    onChange={(e) => setSurvey({
                      ...survey,
                      design: { ...survey.design, inputBackgroundColor: e.target.value },
                    })}
                    className="block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                    color={survey?.design?.inputBackgroundColor}
                    placeholder="#f9fafb"
                  />
                </div>
              </div>

              <div className="w-full">
                <label htmlFor="inputBorderColor" className="block text-sm font-medium text-black">
                  <div className="flex items-center gap-2">Input Border</div>
                </label>
                <div className="mt-2 flex w-full rounded-md shadow-sm">
                  <ColorPickerInput
                    onChange={(e) => setSurvey({
                      ...survey,
                      design: { ...survey.design, inputBorderColor: e.target.value },
                    })}
                    className="block w-full flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
                    color={survey?.design?.inputBorderColor}
                    placeholder="#e5e7eb"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageWrapper>
  );
}

function SettingsPage() {
  const { survey, setSurvey } = useContext(EditorContext);
  const { workspace } = useUser();
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);

  const handleBranding = () => {
    if(workspace.free && survey.settings?.hideBranding === false) {
      setShowUpgradeModal(true);
    } else {
      setSurvey({
        ...survey,
        settings: {
          ...survey.settings,
          hideBranding: !survey.settings?.hideBranding,
        },
      });
    }
  };

  return (
    <PageWrapper>
      <PageHeader title={'Settings'} description={'Configure your survey settings'} />
      <div className="">
        <div className="flex flex-col ">
          <label htmlFor="title" className="block text-sm font-medium text-black">
            <div className="flex items-center gap-2 mb-2">Survey Name</div>
          </label>
          <div className=" flex w-full rounded-md shadow-sm mb-4">
            <input
              name="title"
              type="text"
              placeholder="ex. Customers Feedback"
              value={survey?.name}
              onChange={(e) => setSurvey({ ...survey, name: e.target.value })}
              className="block flex-grow rounded-md rounded-r-md border border-gray-300 p-2.5 focus:border-black focus:ring-black disabled:opacity-60"
            />
          </div>
          {/* Language Selection */}
          <div className="w-full mb-4">
            <label htmlFor="languageSelector" className="block text-sm font-medium text-black">
              <div className="flex items-center gap-2 mb-2">Survey Language</div>
            </label>
            <LanguageSelector
              selectedLanguage={survey?.settings?.language || 'en'}
              onLanguageChange={(languageCode) => {
                let newFont = survey?.design?.font || 'Nunito';
                if(languageCode === 'he' && survey?.settings?.language !== 'he') {
                  newFont = 'Open Sans';
                }
                setSurvey({
                  ...survey,
                  settings: { ...survey.settings, language: languageCode },
                  design: { ...survey.design, font: newFont },
                });
              }}
              showLabel={false}
            />
          </div>

          <div className="w-full">
            <label htmlFor="maxQuestions" className="block text-sm font-medium text-black">
              <div className="flex items-center gap-2">Maximum Questions</div>
            </label>
            <div className="mt-2">
              <InputSlider
                value={survey?.settings?.maxQuestions}
                onChange={(value) => setSurvey({
                  ...survey,
                  settings: { ...survey.settings, maxQuestions: value },
                })}
                min={3}
                max={10}
                step={1}
                defaultValue={8}
              />
            </div>
            <p className="mt-1 text-xs text-gray-500 mb-3">
              Maximum number of questions the AI can ask (3-10).
            </p>
          </div>
          {/* branding toggle */}
          <div className={'flex w-full items-center justify-between rounded-lg border p-2.5 pl-3 shadow-sm mb-3'}>
            <label htmlFor="brandingToggle" className="block text-sm font-medium text-black">
              <div className="flex items-center gap-2">
                <span>Hide Shapo Branding</span>
                {workspace.free && <ProBadge text={'Pro feature'} />}
              </div>
            </label>
            <Toggle
              value={survey?.settings?.hideBranding}
              onChange={(checked) => {
                if(workspace.free && !checked) {
                  setShowUpgradeModal(true);
                } else {
                  setSurvey({
                    ...survey,
                    settings: { ...survey.settings, hideBranding: checked },
                  });
                }
              }}
            />
          </div>

          <p className="text-xs text-gray-500 -mt-1">
            Hide "Powered by Shapo" footer on your survey
          </p>
          <UpgradeModal
            message={(
              <>
                <span className="font-semibold">Only Pro users can hide the Shapo branding.</span>
                <br />
                <br />
                Consider upgrading your workspace plan to unlock all features and enjoy unlimited usage!
              </>
            )}
            showUpgradeModal={showUpgradeModal}
            setShowUpgradeModal={setShowUpgradeModal}
          />
        </div>
      </div>
    </PageWrapper>
  );
}

function AnalyticsPage() {
  const { survey } = useContext(EditorContext);
  const { workspace } = useUser();

  return (
    <div className="h-full">
      <SurveyAnalytics workspaceId={workspace.id} surveyId={survey._id} embedded isFree={workspace.free} />
    </div>
  );
}

function ResponsesPage() {
  const { survey } = useContext(EditorContext);
  const { workspace } = useUser();
  const [selectedResponse, setSelectedResponse] = useState(null);
  const [showConversation, setShowConversation] = useState(false);
  const [sentimentFilter, setSentimentFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateFilter, setDateFilter] = useState('all');
  const [analytics, setAnalytics] = useState(null);
  const [analyticsLoading, setAnalyticsLoading] = useState(true);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [generatingInsights, setGeneratingInsights] = useState(false);

  const {
    responses,
    total,
    currentPage,
    totalPages,
    hasMore,
    limit,
    isLoading,
    error,
    goToPage,
    setLimit,
  } = useSurveyResponses({
    workspaceId: workspace?.id,
    surveyId: survey?._id,
    page: 1,
    limit: 24,
    completed: statusFilter === 'all' ? undefined : statusFilter === 'completed',
    sentiment: sentimentFilter,
    date: dateFilter,
  });

  useEffect(() => {
    if(survey && workspace.id && survey._id) {
      // Initial load is handled by useSWR, but we can refetch if needed
      // For now, we rely on useSWR to manage the data fetching.
    }
  }, [workspace.id, survey?._id]);

  // Load analytics data
  useEffect(() => {
    const loadAnalytics = async () => {
      if(!workspace?.id || !survey?._id) {
        return;
      }

      try {
        setAnalyticsLoading(true);
        const analyticsData = await surveyService.getSurveyAnalytics(workspace.id, survey._id);
        setAnalytics(analyticsData);
      } catch(error) {
        console.error('Failed to load analytics:', error);
      } finally {
        setAnalyticsLoading(false);
      }
    };

    loadAnalytics();
  }, [workspace?.id, survey?._id]);

  // Map language codes to moment locales (some codes differ)
  const getMomentLocale = (code) => {
    const localeMap = {
      en: 'en',
      es: 'es',
      fr: 'fr',
      de: 'de',
      it: 'it',
      pt: 'pt',
      ru: 'ru',
      zh: 'zh-cn',
      ja: 'ja',
      ko: 'ko',
      ar: 'ar',
      he: 'he',
      fa: 'fa',
      ur: 'ur',
    };
    return localeMap[code] || 'en';
  };

  // Dynamic locale loading effect
  useEffect(() => {
    const loadLocale = async () => {
      const languageCode = survey?.settings?.language || 'en';
      const momentLocale = getMomentLocale(languageCode);

      // Skip if it's English (default) or already loaded
      if(momentLocale === 'en' || moment.locale() === momentLocale) {
        return;
      }

      try {
        // Dynamic import using template literal
        await import(`moment/locale/${momentLocale}`);

        // Set the locale after loading
        moment.locale(momentLocale);
      } catch(error) {
        console.warn('Failed to load locale:', momentLocale, error);
        // Fallback to English
        moment.locale('en');
      }
    };

    loadLocale();
  }, [survey?.settings?.language]);

  const formatDate = (dateString) => {
    moment.locale('en');
    const date = moment(dateString);
    const now = moment();
    if(date.year() !== now.year()) {
      return date.format('MMM D, YYYY h:mm A');
    }
    return date.format('MMM D h:mm A');
  };

  const formatDistanceToNow = (dateString) => {
    moment.locale('en');
    return moment(dateString).fromNow();
  };

  const getParticipantName = (response) => {
    const userAgent = response.metadata?.userAgent || '';
    if(userAgent.includes('Mozilla')) {
      return 'Anonymous User';
    }
    return 'Anonymous User';
  };

  const getLastResponseTime = (response) => {
    const userMessages = response.conversation?.filter((msg) => msg.role === 'user') || [];
    if(userMessages.length > 0) {
      const lastMessage = userMessages[userMessages.length - 1];
      return lastMessage.timestamp || response.updatedAt || response.createdAt;
    }
    return response.createdAt;
  };

  const handleViewConversation = (response) => {
    setSelectedResponse(response);
    setShowConversation(true);
  };

  const handleBackToResponses = () => {
    setShowConversation(false);
    setSelectedResponse(null);
  };

  const handlePageChange = (pageNumber) => {
    goToPage(pageNumber);
    const responsesSection = document.getElementById('responses-section');
    if(responsesSection) {
      responsesSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  const handleSentimentFilterChange = (newFilter) => {
    setSentimentFilter(newFilter);
    goToPage(1);
  };

  const handleStatusFilterChange = (newFilter) => {
    setStatusFilter(newFilter);
    goToPage(1);
  };

  const handleDateFilterChange = (newFilter) => {
    setDateFilter(newFilter);
    goToPage(1);
  };

  const generateAllAI = async () => {
    if(workspace.free) {
      setShowUpgradeModal(true);
      return;
    }
    try {
      setGeneratingInsights(true);
      await surveyService.generateInsights(workspace.id, survey._id);
      await surveyService.generateReport(workspace.id, survey._id);

      toast.dismiss();
      toast.success('AI insights and report generated successfully');

      // Reload analytics data to get new insights and report
      const analyticsData = await surveyService.getSurveyAnalytics(workspace.id, survey._id);
      setAnalytics(analyticsData);
    } catch(error) {
      console.error('Failed to generate AI content:', error);
      toast.dismiss();
      toast.error('Failed to generate AI content');
    } finally {
      setGeneratingInsights(false);
    }
  };

  if(isLoading) {
    return (
      <PageWrapper>
        <div className="space-y-4">
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="bg-white rounded-lg border shadow-sm animate-pulse">
              <div className="px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="p-2 bg-gray-200 rounded-lg mr-3 animate-pulse">
                      <div className="w-5 h-5 bg-gray-300 rounded" />
                    </div>
                    <div>
                      <div className="h-5 bg-gray-200 rounded w-32 mb-2" />
                      <div className="h-3 bg-gray-200 rounded w-20" />
                    </div>
                  </div>
                  <div className="h-6 bg-gray-200 rounded-full w-16" />
                </div>
                <div className="mt-4 space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-24" />
                  <div className="h-4 bg-gray-200 rounded w-32" />
                  <div className="h-4 bg-gray-200 rounded w-28" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </PageWrapper>
    );
  }

  if(error) {
    return (
      <PageWrapper>
        <div className="bg-white rounded-lg border shadow-sm">
          <div className="text-center py-16">
            <AlertTriangle className="w-16 h-16 text-red-300 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">Error loading responses</h3>
            <p className="text-gray-500">
              {error.message || 'Failed to load survey responses'}
            </p>
          </div>
        </div>
      </PageWrapper>
    );
  }

  if(showConversation && selectedResponse) {
    return (
      <div className="h-screen flex flex-col">
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <button
                onClick={handleBackToResponses}
                className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 mr-4"
              >
                <ArrowLeft className="w-4 h-4 mr-1" />
                Back
              </button>
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  Conversation with {getParticipantName(selectedResponse)}
                </h1>
                <p className="text-sm text-gray-600">
                  Survey: {survey?.title}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="flex-1 flex overflow-hidden">
          <div className="flex-1 flex flex-col mb-10">
            <div className="bg-white rounded-lg border shadow-sm h-full flex flex-col mx-4 my-4">
              <div className="flex-1 overflow-hidden rounded-lg">
                <div className="h-full overflow-y-auto">
                  <div className="px-4 py-6 space-y-4 ">
                    {selectedResponse.conversation && selectedResponse.conversation.length > 0 ? (
                      selectedResponse.conversation.map((message, index) => {
                        const isUser = message.role === 'user';
                        const isAssistant = message.role === 'assistant';

                        return (
                          <div key={index} className={`flex ${isUser ? 'justify-end' : 'justify-start'}`}>
                            <div className={`flex max-w-[75%] ${isUser ? 'flex-row-reverse' : 'flex-row'}`}>
                              <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center shadow-sm ${
                                isAssistant
                                  ? 'bg-blue-600 text-white'
                                  : 'bg-gray-700 text-white'
                              } ${isUser ? 'ml-3' : 'mr-3'}`}
                              >
                                {isAssistant ? (
                                  <Bot className="w-4 h-4" />
                                ) : (
                                  <User className="w-4 h-4" />
                                )}
                              </div>
                              <div className="flex flex-col">
                                <div className={`px-4 py-3 rounded-2xl shadow-sm ${
                                  isAssistant
                                    ? 'bg-white text-gray-900 border border-gray-200 rounded-tl-md'
                                    : 'bg-blue-600 text-white rounded-tr-md'
                                }`}
                                >
                                  <p className="text-base leading-relaxed whitespace-pre-wrap">
                                    {message.content}
                                  </p>
                                </div>
                                {message.timestamp && (
                                  <div className={`text-xs text-gray-400 mt-1 ${isUser ? 'text-right' : 'text-left'}`}>
                                    {formatDate(message.timestamp)}
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        );
                      })
                    ) : (
                      <div className="text-center py-12">
                        <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                          <MessageSquare className="w-8 h-8 text-gray-400" />
                        </div>
                        <h3 className="text-lg font-medium text-gray-900 mb-2">No messages yet</h3>
                        <p className="text-gray-600">This conversation hasn't started yet.</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="px-6 py-4 border-t border-gray-200 bg-gray-50 rounded-b-lg">
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <span>Conversation with {getParticipantName(selectedResponse)}</span>
                  <span>{selectedResponse.conversation?.length || 0} messages</span>
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>
    );
  }

  return (
    <PageWrapper>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Survey Responses & Analytics</h2>
        <p className="text-gray-600">View and analyze responses from your survey participants with AI-powered insights.</p>
      </div>

      {/* Analytics Summary Cards */}
      {!analyticsLoading && analytics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <MessageSquare className="w-5 h-5 text-blue-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Total Responses</p>
                <p className="text-xl font-bold text-gray-900">
                  {analytics.stats?.totalResponses || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Users className="w-5 h-5 text-green-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-xl font-bold text-gray-900">
                  {analytics.stats?.completedResponses || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <TrendingUp className="w-5 h-5 text-purple-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Response Rate</p>
                <p className="text-xl font-bold text-gray-900">
                  {analytics.stats?.responseRate ? `${analytics.stats.responseRate.toFixed(1)}%` : '0%'}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Star className="w-5 h-5 text-yellow-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-600">Avg. Satisfaction</p>
                <p className="text-xl font-bold text-gray-900">
                  {analytics.stats?.avgSentimentScore ? `${(analytics.stats.avgSentimentScore * 5).toFixed(1)}/5` : 'N/A'}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {!analyticsLoading && analytics && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-6">
          <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Lightbulb className="w-6 h-6 text-blue-600 mr-2" />
                <h3 className="text-xl font-bold text-gray-900">Detailed Insights</h3>
              </div>
              <button
                onClick={generateAllAI}
                disabled={generatingInsights || !analytics?.stats?.completedResponses}
                className="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed shadow-sm hover:shadow-lg"
              >
                {generatingInsights ? (
                  <span className="flex items-center">
                    <RefreshCw className="animate-spin w-4 h-4 mr-2" />
                    Generating...
                  </span>
                ) : (
                  <span className="flex items-center">
                    <Sparkles className="w-4 h-4 mr-2" />
                    Generate Insights
                  </span>
                )}
              </button>
            </div>
          </div>
          <div className="p-6">
            {generatingInsights ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Array.from({ length: 6 }).map((_, idx) => (
                  <div
                    key={idx}
                    className="bg-gray-50 rounded-lg p-4 border border-gray-200 animate-pulse"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="h-4 bg-gray-300 rounded w-3/4" />
                      <div className="h-3 bg-gray-300 rounded w-16" />
                    </div>

                    <div className="space-y-2">
                      <div className="h-3 bg-gray-300 rounded w-full" />
                      <div className="h-3 bg-gray-300 rounded w-5/6" />
                      <div className="h-3 bg-gray-300 rounded w-4/6" />
                    </div>
                  </div>
                ))}
              </div>
            ) : analytics.insights && analytics.insights.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {analytics.insights.slice(0, 6).map((insight, idx) => (
                  <div
                    key={idx}
                    className="bg-gray-50 rounded-lg p-4 border border-gray-200 hover:shadow-md transition-shadow duration-200"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <h4 className="font-semibold text-gray-900 mb-2">
                        {insight.title}
                      </h4>

                      {insight.percentage && (
                        <div className="flex items-center">
                          <span className="text-xs text-gray-500">
                            {insight.percentage}% of responses
                          </span>
                        </div>
                      )}
                    </div>

                    <p className="text-gray-600 text-sm leading-relaxed">
                      {insight.description}
                    </p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Lightbulb className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No insights yet</h3>
                <p className="text-gray-600">Generate AI insights to get detailed analysis of your survey responses.</p>
              </div>
            )}
          </div>
        </div>
      )}

      <UpgradeModal
        message={<><span className="font-semibold">Only Pro users can generate AI insights and report.</span><br /><br />Consider upgrading your workspace plan to unlock all features and enjoy unlimited usage!</>}
        showUpgradeModal={showUpgradeModal}
        setShowUpgradeModal={setShowUpgradeModal}
      />

      <div className="mt-4 p-4 border border-gray-300 rounded-lg mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">Status:</label>
              <div className="relative">
                <select
                  value={statusFilter}
                  onChange={(e) => handleStatusFilterChange(e.target.value)}
                  className="bg-white border border-gray-300 rounded-lg px-3 pr-8 text-sm font-medium text-gray-700 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  style={{
                    appearance: 'none',
                    WebkitAppearance: 'none',
                    MozAppearance: 'none',
                    backgroundImage: 'none',
                  }}
                >
                  <option value="all">All Status</option>
                  <option value="completed">Completed</option>
                  <option value="incomplete">In Progress</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <ChevronDown className="w-4 h-4 text-gray-400" />
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">Sentiment:</label>
              <div className="relative">
                <select
                  value={sentimentFilter}
                  onChange={(e) => handleSentimentFilterChange(e.target.value)}
                  className="bg-white border border-gray-300 rounded-lg px-3 pr-8 text-sm font-medium text-gray-700 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  style={{
                    appearance: 'none',
                    WebkitAppearance: 'none',
                    MozAppearance: 'none',
                    backgroundImage: 'none',
                  }}
                >
                  <option value="all">All Sentiment</option>
                  <option value="positive">Positive</option>
                  <option value="neutral">Neutral</option>
                  <option value="negative">Negative</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <ChevronDown className="w-4 h-4 text-gray-400" />
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">Date:</label>
              <div className="relative">
                <select
                  value={dateFilter}
                  onChange={(e) => handleDateFilterChange(e.target.value)}
                  className="bg-white border border-gray-300 rounded-lg px-3 pr-8 text-sm font-medium text-gray-700 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  style={{
                    appearance: 'none',
                    WebkitAppearance: 'none',
                    MozAppearance: 'none',
                    backgroundImage: 'none',
                  }}
                >
                  <option value="all">All Time</option>
                  <option value="today">Today</option>
                  <option value="week">Last Week</option>
                  <option value="month">Last Month</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                  <ChevronDown className="w-4 h-4 text-gray-400" />
                </div>
              </div>
            </div>
            <button
              onClick={() => {
                setStatusFilter('all');
                setSentimentFilter('all');
                setDateFilter('all');
                goToPage(1);
              }}
              className="px-3 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200"
            >
              Clear All
            </button>
          </div>
        </div>
      </div>
      {responses.length === 0 ? (
        <div className="bg-white rounded-lg border shadow-sm">
          <div className="text-center py-16">
            <Users className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">No responses yet</h3>
            <p className="text-gray-500">
              Once people start taking your survey, their responses will appear here.
            </p>
          </div>
        </div>
      ) : (
        <div id="responses-section" className="space-y-6">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            total={total}
            limit={limit}
            onPageChange={handlePageChange}
            onLimitChange={setLimit}
          />

          <div className="space-y-4">
            {responses.length === 0 ? (
              <div className="bg-white rounded-xl border border-gray-200 p-8 text-center">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <MessageSquare className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No responses found</h3>
                <p className="text-gray-500 mb-4">
                  {sentimentFilter === 'all'
                    ? 'No responses match your current filter.'
                    : `No ${sentimentFilter} responses found. Try changing the sentiment filter.`}
                </p>
                {sentimentFilter !== 'all' && (
                <button
                  onClick={() => handleSentimentFilterChange('all')}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors duration-200"
                >
                  Show all responses
                </button>
                )}
              </div>
            ) : (
              responses.map((response) => (
                <div key={response._id} className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200">
                  <div className="p-4">
                    <div className="flex items-center">
                      <div className="flex items-center space-x-3 w-1/4">
                        <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                          <Calendar className="w-4 h-4 text-gray-600" />
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900">
                            {formatDistanceToNow(getLastResponseTime(response))}
                          </p>
                          <p className="text-xs text-gray-500">
                            {formatDate(getLastResponseTime(response))}
                          </p>
                          <div className="">
                            <span className={`inline-flex items-center text-xs  ${
                              response.isComplete
                                ? 'text-green-600'
                                : 'text-blue-600'
                            }`}
                            >
                              {response.isComplete ? 'Completed' : 'In Progress'}
                            </span>
                          </div>
                        </div>

                      </div>
                      <div className="w-px h-12 bg-gray-200 mx-4" />
                      <div className="flex-1 px-4">
                        {response.analysis?.summary ? (
                          <div>
                            <div className="flex items-center space-x-2 mb-2">
                              <div className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center">
                                <Sparkles className="w-3 h-3 text-blue-600" />
                              </div>
                              <h4 className="text-sm font-semibold text-gray-900">AI Summary</h4>
                            </div>
                            <p className="text-sm text-gray-700 leading-relaxed">
                              {response.analysis.summary}
                            </p>
                          </div>
                        ) : (
                          <div className="text-sm text-gray-500 italic">
                            No AI summary available
                          </div>
                        )}
                      </div>

                      <div className="w-px h-12 bg-gray-200 mx-4" />
                      <div className="w-1/3 flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="text-center">
                            <div className="text-lg font-bold text-gray-900">
                              {response.conversation?.length || 0}
                            </div>
                            <div className="text-xs text-gray-600">Total</div>
                          </div>
                          <div className="text-center">
                            <div className="text-lg font-bold text-gray-900">
                              {response.conversation?.filter((m) => m.role === 'user').length || 0}
                            </div>
                            <div className="text-xs text-gray-600">User</div>
                          </div>
                          <div className="text-center">
                            <div className="text-lg font-bold text-gray-900">
                              {response.conversation?.filter((m) => m.role === 'assistant').length || 0}
                            </div>
                            <div className="text-xs text-gray-600">AI</div>
                          </div>
                        </div>

                        <div className="flex items-center space-x-3">
                          {response.sentimentScore && (
                          <div className="flex items-center space-x-1 mr-3">
                            {[1, 2, 3, 4, 5].map((circle) => {
                              const score = response.sentimentScore * 5;
                              const isFilled = circle <= Math.floor(score);
                              const isPartial = circle === Math.ceil(score) && score % 1 !== 0;
                              const fillPercentage = isPartial ? (score % 1) * 100 : 100;

                              return (
                                <div key={circle} className="relative w-3 h-3">
                                  <div className="w-3 h-3 rounded-full bg-gray-200" />
                                  {(isFilled || isPartial) && (
                                  <div
                                    className={`absolute top-0 left-0 w-3 h-3 rounded-full ${
                                      response.sentiment === 'positive' ? 'bg-green-500'
                                        : response.sentiment === 'negative' ? 'bg-red-500' : 'bg-yellow-500'
                                    }`}
                                    style={{
                                      clipPath: isPartial
                                        ? `polygon(0 0, ${fillPercentage}% 0, ${fillPercentage}% 100%, 0 100%)`
                                        : 'polygon(0 0, 100% 0, 100% 100%, 0 100%)',
                                    }}
                                  />
                                  )}
                                </div>
                              );
                            })}
                            <span className="text-sm font-medium text-gray-900 ml-2">
                              {(response.sentimentScore * 5).toFixed(1)}
                            </span>
                          </div>
                          )}
                          <button
                            onClick={() => handleViewConversation(response)}
                            className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg text-gray-700 bg-gray-100 hover:bg-gray-200 transition-colors duration-200"
                          >
                            <MessageSquare className="w-4 h-4 mr-1.5" />
                            View
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Bottom Pagination */}
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            total={total}
            limit={limit}
            onPageChange={handlePageChange}
            onLimitChange={setLimit}
          />
        </div>
      )}

    </PageWrapper>
  );
}

function ColorPickerInput({ color, onChange, className, placeholder }) {
  return (
    <div className="relative flex w-full items-center">
      <div className="relative flex items-center hover:opacity-75">
        <button
          className="absolute ml-2 h-7 w-7 cursor-pointer rounded-full border border-gray-300"
          style={{ backgroundColor: color }}
        />
        <div className="absolute cursor-pointer">
          <input
            type="color"
            value={color}
            onChange={onChange}
            className="ml-2 h-7 w-7 cursor-pointer opacity-0"
            name=""
            id=""
          />
        </div>
      </div>
      <input
        type="text"
        value={color}
        placeholder={placeholder}
        onChange={onChange}
        className={`${className} pl-12 font-bold text-gray-700`}
      />
    </div>
  );
}

export default SurveyEditor;
