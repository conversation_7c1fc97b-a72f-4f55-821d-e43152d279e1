@import url('https://fonts.bunny.net/css2?family=Nunito:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;0,1000;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900;1,1000&display=swap');
@import url('https://fonts.bunny.net/css2?family=Varela+Round:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;0,1000;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900;1,1000&display=swap');
@import url('https://fonts.bunny.net/css2?family=Open+Sans:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;0,1000;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900;1,1000&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
  padding-right: 0 !important;
  padding: 0;
  margin: 0;
  font-family: Nunito, 'Open Sans' ,sans-serif, serif !important;
}

input,
textarea,
button,
select,
a {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.hebrew-font {
  font-family: 'Varela Round', sans-serif, serif !important;
}

#HW_frame_cont {
  display: none;
}
#HW_badge_cont {
  display: none;
}

.page-wrapper {
  scroll-margin-top: 100px;
}

button {
  outline: 0 !important;
  box-shadow: 0 0 0 0 rgba(0, 0, 0, 0) !important;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

*,
*:focus,
*:hover,
button,
button:focus,
button:hover {
  outline: none !important;
}

textarea,
input[type='password'],
input[type='number'],
input[type='text'],
input[type='date'],
input[type='email'] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.inset-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

select {
  background: url(data:image/svg+xml;base64,PHN2ZyBzdHJva2U9ImN1cnJlbnRDb2xvciIgZmlsbD0iY3VycmVudENvbG9yIiBzdHJva2Utd2lkdGg9IjAiIHZpZXdCb3g9IjAgMCAxNiAxNiIgaGVpZ2h0PSIxZW0iIHdpZHRoPSIxZW0iIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBkPSJNMS42NDYgNC42NDZhLjUuNSAwIDAgMSAuNzA4IDBMOCAxMC4yOTNsNS42NDYtNS42NDdhLjUuNSAwIDAgMSAuNzA4LjcwOGwtNiA2YS41LjUgMCAwIDEtLjcwOCAwbC02LTZhLjUuNSAwIDAgMSAwLS43MDh6Ij48L3BhdGg+PC9zdmc+)
    no-repeat 100% 50%;
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
  height: 30px;
  width: 100%;
}

.direction-rtl {
  direction: rtl !important;
}

.direction-ltr {
  direction: ltr !important;
}

.image-gallery-left-nav .image-gallery-svg {
  height: 40px !important;
  width: 40px !important;
}
.image-gallery-right-nav .image-gallery-svg {
  height: 40px !important;
  width: 40px !important;
}

.image-gallery-content:not(.fullscreen) .image-gallery-slide .image-gallery-image {
  max-height: 215px !important;
}

.image-gallery-thumbnail-image {
  height: 50px !important;
  width: 50px !important;
}

.image-gallery-thumbnail-inner {
  height: 50px !important;
  width: 50px !important;
}
.image-gallery-thumbnail {
  width: auto !important;
}

.image-gallery-thumbnail .image-gallery-thumbnail-image {
  object-fit: cover !important;
}

.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}
/* Hide scrollbar for IE, Edge and Firefox */
.no-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

/* Webkit Browsers (Chrome, Safari, Edge) */
#testimonial-scrollbar::-webkit-scrollbar-track {
  background-color: transparent;
  border-radius: 10px;
}

#testimonial-scrollbar::-webkit-scrollbar {
  width: 3px;
  background-color: #efefef;
  border-radius: 10px;
}

#testimonial-scrollbar::-webkit-scrollbar-thumb {
  background-color: #000000;
}

/* Firefox-only styles */
@-moz-document url-prefix() {
  #testimonial-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #000000 #efefef;
  }
}

.simplebar-scrollbar {
  width: 8px;
}
